// lib/src/core/theme/app_colors.dart

import 'package:flutter/material.dart';

// PRD 规范中的颜色定义 - V2 (复刻竞品)
class AppColors {
  // --- 基础色 ---
  static const Color background = Color(0xFF121212); // 主背景色
  static const Color secondaryBg = Color(0xFF1C1C1E); // 次级背景/卡片背景
  static const Color inputBackground = Color(0xFF2C2C2E); // 输入框/控件背景

  // --- 文本色 ---
  static const Color primaryText = Color(0xFFFFFFFF);   // 主要文字
  static const Color secondaryText = Color(0xFFAAAAAA); // 次要/提示文字
  static const Color tertiaryText = Color(0xFF888888);  // 最次要文字

  // --- 强调色 (核心！) ---
  static const Color accentPurple = Color(0xFFDA70D6); // 霓虹粉紫 (用于按钮、标签、选中状态)
  static const Color accentYellow = Color(0xFFFFC107); // 柠檬黄 (用于高亮、VIP、特殊按钮)
  static const Color accentGreen = Color(0xFF4CAF50);  // 绿色 (用于成功、完成状态)
  static const Color accentBlue = Color(0xFF2196F3);   // 蓝色 (用于链接、特殊标签)
  static const Color accentOrange = Color(0xFFFF9800); // 橙色 (用于警告、特殊标签)

  // --- 背景渐变色 (核心！) ---
  static const Color primaryGradientStart = Color(0xFF2A1B3D); // 深邃紫 (渐变起始)
  static const Color primaryGradientEnd = Color(0xFF44318D);   // 明亮紫 (渐变结束)

  // --- 辉光/特效色 (核心！) ---
  static const Color glowPurple = Color(0x99E0B0FF); // 紫色辉光 (半透明)
  
  // --- 卡片背景色 ---
  static const Color cardBackground = Color(0xFF2C2C2E); // 卡片背景色
} 
