// lib/src/features/profile/presentation/pages/character_detail_page.dart

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../bloc/character_detail_bloc.dart';
import '../../repository/character_detail_repository.dart';
import '../../repository/user_preferences_repository.dart';
import 'package:xinglian/src/features/profile/presentation/widgets/character_content_tabs.dart';
import 'package:xinglian/src/features/profile/presentation/widgets/character_header_info.dart';
import 'package:xinglian/src/common/widgets/pulsing_button.dart'; // <--- 1. 导入新组件


// Top-level Widget
class CharacterDetailPage extends StatelessWidget {
  final String agentId; 
  const CharacterDetailPage({super.key, required this.agentId});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      // --- v 修正开始 v ---
      create: (context) => CharacterDetailBloc(
        context.read<CharacterDetailRepository>(),
        context.read<UserPreferencesRepository>(),
      )..add(LoadCharacterDetails(agentId)), // 现在传递的是agentId
      // --- ^ 修正结束 ^ ---
      child: const _CharacterDetailView(),
    );
  }
}


// View Widget
class _CharacterDetailView extends StatelessWidget {
  const _CharacterDetailView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent, // 背景由Stack处理
      body: BlocBuilder<CharacterDetailBloc, CharacterDetailState>(
        builder: (context, state) {
          if (state is CharacterDetailLoading || state is CharacterDetailInitial) {
            return const Center(child: CircularProgressIndicator());
          }
          if (state is CharacterDetailLoaded) {
            return Stack(
              children: [
                _buildBackground(state.details.agent.imageUrl ?? ''),
                _buildBody(state),
              ],
            );
          }
          if (state is CharacterDetailError) {
            return Center(child: Text(state.message));
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }
  
  Widget _buildBackground(String imageUrl) {
    return Stack(
      children: [
        // Ensure imageUrl is not empty before creating a NetworkImage
        if (imageUrl.isNotEmpty)
          Image.network(imageUrl, width: double.infinity, height: double.infinity, fit: BoxFit.cover),
        BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(color: Colors.black.withOpacity(0.5)),
        ),
      ],
    );
  }
  
  Widget _buildBody(CharacterDetailLoaded state) {
    return DefaultTabController(
      length: 4,
      child: SafeArea(
        child: Column(
          children: [
            const _AppBar(),
            Expanded(child: _DetailPageContent(state: state)),
          ],
        ),
      ),
    );
  }
}


// --- 分离出的UI组件 ---

class _AppBar extends StatelessWidget {
  const _AppBar();
  @override
  Widget build(BuildContext context) {
     return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(icon: const Icon(Icons.close, color: Colors.white), onPressed: () => context.pop()),
          IconButton(icon: const Icon(Icons.more_horiz, color: Colors.white), onPressed: () {}),
        ],
      ),
    );
  }
}

class _DetailPageContent extends StatelessWidget {
  final CharacterDetailLoaded state;
  const _DetailPageContent({required this.state});
  
  @override
  Widget build(BuildContext context) {
     return Column(
      children: [
        CharacterHeaderInfo(details: state.details),
        CharacterContentTabs(state),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          // --- v 核心修改 v ---
          child: PulsingButton( // 2. 将 ElevatedButton 替换为 PulsingButton
            onPressed: () {
              // 点击聊天按钮，跳转到聊天页面
              context.push('/ai-chat/${state.details.agent.id}');
            },
            text: '聊天',
            backgroundColor: AppColors.accentYellow,
            textColor: Colors.black,
          ),
          // --- ^ 核心修改 ^ ---
        ),
      ],
    );
  }
}
