// lib/src/features/chat/presentation/widgets/message_list.dart (REFACTORED)

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:just_audio/just_audio.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/chat/models/message_model.dart';
import 'package:xinglian/src/features/discovery/models/agent_model.dart';
import 'package:xinglian/src/features/chat/bloc/chat_player/chat_player_bloc.dart';
import 'package:xinglian/src/common/widgets/typewriter_text.dart';


class MessageList extends StatefulWidget {
  final List<Message> messages;
  final List<Agent> participants;
  final bool isStoryMode;
  final EdgeInsets? customPadding; // 新增：自定义padding
  final Agent? protagonistAgent; // 新增：主角Agent信息

  const MessageList({
    super.key,
    required this.messages,
    required this.participants,
    this.isStoryMode = false,
    this.customPadding, // 新增参数
    this.protagonistAgent, // 主角Agent信息
  });

  @override
  State<MessageList> createState() => _MessageListState();
}

class _MessageListState extends State<MessageList> {
  final ScrollController _scrollController = ScrollController();
  final List<Message> _internalMessages = [];

  @override
  void initState() {
    super.initState();
    // 初始加载时不使用动画
    _internalMessages.addAll(widget.messages);
    
    // 添加滚动监听器，用于无限滚动加载更多历史消息
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    // 检查是否滚动到顶部（因为列表是reverse的，所以检查maxScrollExtent）
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 100) {
      // 当接近顶部时，触发加载更多消息
      final bloc = context.read<ChatPlayerBloc>();
      if (bloc.state.hasMoreMessages && !bloc.state.isReplying) {
        bloc.add(const LoadMoreMessages());
      }
    }
  }

  @override
  void didUpdateWidget(covariant MessageList oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 简化逻辑：直接同步消息列表，ListView.builder会自动处理更新
    if (widget.messages != oldWidget.messages) {
      setState(() {
        _internalMessages.clear();
        _internalMessages.addAll(widget.messages);
      });

      // 如果有新消息添加，滚动到底部
      if (widget.messages.length > oldWidget.messages.length) {
        WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
      }
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.minScrollExtent, // 滚动到底部（因为是reverse）
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }
  
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_internalMessages.isEmpty) {
      return const Center(child: Text('开始对话吧...', style: TextStyle(color: Colors.white54)));
    }

    return ListView.builder(
      key: ValueKey('message_list_${_internalMessages.length}'), // 使用key来帮助Flutter识别
      controller: _scrollController,
      padding: widget.customPadding ?? const EdgeInsets.fromLTRB(16, 100, 16, 200),
      itemCount: _internalMessages.length,
      reverse: true, // 保持消息从底部开始
      itemBuilder: (context, index) {
        final reversedIndex = _internalMessages.length - 1 - index;
        final message = _internalMessages[reversedIndex];
        final agent = widget.participants.where((p) => p.id == message.agentId).isNotEmpty
            ? widget.participants.firstWhere((p) => p.id == message.agentId)
            : null;

        return MessageBubble(
          key: ValueKey('bubble_${message.id}'),
          message: message,
          agent: agent,
          isStoryMode: widget.isStoryMode,
          isHistoryMessage: message.isHistoryMessage,
          protagonistAgent: widget.protagonistAgent,
        );
      },
    );
  }
}

class MessageBubble extends StatefulWidget {
  final Message message;
  final Agent? agent;
  final bool isStoryMode;
  final bool isHistoryMessage; // 新增：是否是历史消息
  final Agent? protagonistAgent; // 新增：主角Agent信息

  const MessageBubble({
    super.key,
    required this.message,
    this.agent,
    required this.isStoryMode,
    this.isHistoryMessage = false, // 默认不是历史消息
    this.protagonistAgent, // 主角Agent信息
  });

  @override
  State<MessageBubble> createState() => _MessageBubbleState();
}

class _MessageBubbleState extends State<MessageBubble> with SingleTickerProviderStateMixin {
  static final _audioPlayer = AudioPlayer();
  static String? _lastAutoPlayedUrl;
  
  // --- v 动画控制器 v ---
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  // --- ^ 动画控制器 ^ ---

  StreamSubscription? _playerStateSubscription;

  @override
  void initState() {
    super.initState();
    // --- v 初始化动画 v ---
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300), // 动画时长
    );

    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack, // 带有回弹效果的曲线
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    );
    // --- ^ 初始化动画 ^ ---

    _playerStateSubscription = _audioPlayer.playerStateStream.listen((state) {
      if (!mounted) return;
      final isCurrent = (_audioPlayer.audioSource as UriAudioSource?)?.tag == widget.message.audioUrl;
      if (state.playing && isCurrent) {
        // (音频播放逻辑不变)
      } else {
        // (音频播放逻辑不变)
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // --- v 触发动画 v ---
      // 只有非历史消息才播放出现动画
      if (!widget.isHistoryMessage) {
        _animationController.forward();
      }
      // --- ^ 触发动画 ^ ---

      if (widget.message.audioUrl != null &&
          widget.message.audioUrl!.isNotEmpty &&
          widget.message.role == MessageRole.assistant &&
          widget.message.audioUrl != _lastAutoPlayedUrl) {
        _playAudio(widget.message.audioUrl!);
        _lastAutoPlayedUrl = widget.message.audioUrl;
      }
    });
  }

  @override
  void dispose() {
    _playerStateSubscription?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _playAudio(String url) async {
    try {
      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }
      final source = AudioSource.uri(Uri.parse(url), tag: url);
      await _audioPlayer.setAudioSource(source);
      _audioPlayer.play();
    } catch (e) {
      print("播放音频失败: $e");
    }
  }

  Widget _buildFormattedText(String text) {
    final List<InlineSpan> children = [];
    String processedText = text.replaceAll(RegExp(r'["""'']'), '');
    final RegExp regExp = RegExp(r'([（(][^）)]*[）)])');

    // 修复：移除对assistant角色的特殊处理，让所有消息都应用统一的富文本格式化逻辑
    // 这样可以确保NPC回复中的括号内容也能正确显示为斜体和灰色
    processedText.splitMapJoin(
      regExp,
      onMatch: (Match match) {
        children.add(TextSpan(
          text: match.group(0),
          style: TextStyle(
            color: Colors.white.withOpacity(0.7), // 调整为稍微明亮一些的灰色
            fontStyle: FontStyle.italic,
            fontSize: 16,
            height: 1.5,
          ),
        ));
        return '';
      },
      onNonMatch: (String nonMatch) {
        if (nonMatch.trim().isNotEmpty) {
          children.add(TextSpan(
            text: nonMatch,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              height: 1.5,
            ),
          ));
        }
        return '';
      },
    );

    return Text.rich(
      TextSpan(children: children),
      key: ValueKey('text_${widget.message.id}'), // 保持唯一key用于流式更新
    );
  }

  @override
  Widget build(BuildContext context) {
    // --- 关键修复：重新定义 isUser 的判断逻辑 ---
    final isProtagonistMessage = widget.isStoryMode &&
                               widget.protagonistAgent != null &&
                               widget.message.agentId == widget.protagonistAgent!.id;

    final isUser = widget.message.role == MessageRole.user || isProtagonistMessage;
    // --- 修复结束 ---

    final isNarration = widget.message.role == MessageRole.narration;

    if (isNarration) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
        child: Text(
          widget.message.content,
          textAlign: TextAlign.left,
          style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14, fontStyle: FontStyle.italic, height: 1.5),
        ),
      );
    }

    // 在故事模式下，确定显示信息
    String displayName;
    String? displayAvatar;

    if (isUser && widget.isStoryMode && widget.protagonistAgent != null) {
      // 在故事模式下，用户消息显示为女主角
      displayName = widget.protagonistAgent!.name;
      displayAvatar = widget.protagonistAgent!.avatarUrl;
    } else if (isUser) {
      // 普通聊天模式下，用户消息显示为"我"
      displayName = "我";
      displayAvatar = null;
    } else {
      // NPC消息
      displayName = widget.agent?.name ?? '角色';
      displayAvatar = widget.agent?.avatarUrl;

      // --- 调试代码：检查agent匹配问题 ---
      if (widget.message.role == MessageRole.assistant && widget.agent == null) {
        print('--- AVATAR DEBUG ---');
        print('Message ID: ${widget.message.id}');
        print('Message Content: "${widget.message.content.length > 20 ? widget.message.content.substring(0, 20) + "..." : widget.message.content}"');
        print('Expected Agent ID: ${widget.message.agentId}');
        print('Agent is null: ${widget.agent == null}');
        print('--------------------');
      }
    }

    final alignment = isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start;
    final bubbleColor = isUser ? AppColors.accentPurple.withOpacity(0.9) : Colors.black.withOpacity(0.5);

    // 构建头像组件
    Widget? avatarWidget;
    // 只有非用户，或者在剧情模式下的用户（主角），才需要头像
    if (!isUser || (isUser && widget.isStoryMode && displayAvatar != null)) {
      if (displayAvatar != null && displayAvatar.isNotEmpty) {
        // --- vvv 核心修复 vvv ---
        avatarWidget = ClipOval(
          child: SizedBox.fromSize(
            size: const Size.fromRadius(20), // 确保尺寸与旧的CircleAvatar一致
            child: Image.network(
              displayAvatar,
              fit: BoxFit.cover,
              alignment: Alignment.topCenter, // <-- 关键：指定从顶部居中裁剪
              errorBuilder: (context, error, stackTrace) {
                // 错误处理，显示一个默认图标
                return Container(
                  color: AppColors.inputBackground,
                  child: const Icon(Icons.person, color: AppColors.secondaryText, size: 20),
                );
              },
            ),
          ),
        );
        // --- ^^^ 核心修复 ^^^ ---
      } else if (!isUser) {
        // 为没有头像的AI提供一个默认图标
        avatarWidget = const CircleAvatar(
          radius: 20,
          child: Icon(Icons.support_agent),
        );
      }
    }

    // 构建消息气泡组件
    final bubbleWidget = Expanded(
      child: Column(
        crossAxisAlignment: alignment,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              displayName,
              style: TextStyle(color: Colors.white.withOpacity(0.8), fontSize: 12),
            ),
          ),
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
              minWidth: 60,
            ),
            margin: EdgeInsets.only(left: isUser ? 60 : 0, right: isUser ? 0 : 60),
            child: CustomPaint(
              painter: BubblePainter(
                color: bubbleColor,
                isUser: isUser,
              ),
              child: Padding(
                padding: EdgeInsets.fromLTRB(
                  isUser ? 16.0 : 20.0,
                  12.0,
                  isUser ? 20.0 : 16.0,
                  16.0,
                ),
                child: widget.isHistoryMessage
                  ? _buildFormattedText(widget.message.content)
                  : TypewriterText(
                      widget.message.content,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        height: 1.5,
                      ),
                      skipAnimation: widget.isHistoryMessage,
                    ),
              ),
            ),
          ),
        ],
      ),
    );

    // 构建完整的Row children列表
    final List<Widget> children = [];
    if (isUser) { // 用户（主角）消息在右边
      children.add(bubbleWidget);
      if (avatarWidget != null) { // 如果需要显示头像
        children.add(const SizedBox(width: 8));
        children.add(avatarWidget);
      }
    } else { // AI消息在左边
      if (avatarWidget != null) {
        children.add(avatarWidget);
        children.add(const SizedBox(width: 8));
      }
      children.add(bubbleWidget);
    }

    // --- v 核心修改：包裹动画 v ---
    // 如果是历史消息，则直接显示，否则应用动画
    if (widget.isHistoryMessage) {
      return _buildBubbleContent(alignment, children);
    } else {
      return FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: _buildBubbleContent(alignment, children),
        ),
      );
    }
    // --- ^ 核心修改 ^ ---
  }

  // --- v 新增方法：将原 build 方法的内容提取出来 v ---
  Widget _buildBubbleContent(CrossAxisAlignment alignment, List<Widget> children) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Column(
        crossAxisAlignment: alignment,
        children: [
          Row(
            mainAxisAlignment: alignment == CrossAxisAlignment.end ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children, // 直接使用构建好的children列表
          ),
        ],
      ),
    );
  }
  // --- ^ 新增方法 ^ ---
}

/// PRD要求：自定义聊天气泡形状，带有指向头像的尖角
class BubblePainter extends CustomPainter {
  final Color color;
  final bool isUser;
  final double cornerRadius;
  final double tailSize;

  BubblePainter({
    required this.color,
    required this.isUser,
    this.cornerRadius = 20.0,
    this.tailSize = 8.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    
    if (isUser) {
      // 用户消息气泡：右下角有尖角
      path.moveTo(cornerRadius, 0);
      path.lineTo(size.width - cornerRadius, 0);
      path.quadraticBezierTo(size.width, 0, size.width, cornerRadius);
      path.lineTo(size.width, size.height - cornerRadius - tailSize);
      path.quadraticBezierTo(size.width, size.height - tailSize, size.width - cornerRadius, size.height - tailSize);
      path.lineTo(size.width - cornerRadius - tailSize, size.height - tailSize);
      // 右下角尖角
      path.lineTo(size.width, size.height);
      path.lineTo(size.width - tailSize, size.height - tailSize);
      path.lineTo(tailSize, size.height - tailSize);
      path.quadraticBezierTo(0, size.height - tailSize, 0, size.height - cornerRadius - tailSize);
      path.lineTo(0, cornerRadius);
      path.quadraticBezierTo(0, 0, cornerRadius, 0);
    } else {
      // AI消息气泡：左下角有尖角
      path.moveTo(cornerRadius + tailSize, 0);
      path.lineTo(size.width - cornerRadius, 0);
      path.quadraticBezierTo(size.width, 0, size.width, cornerRadius);
      path.lineTo(size.width, size.height - cornerRadius);
      path.quadraticBezierTo(size.width, size.height, size.width - cornerRadius, size.height);
      path.lineTo(cornerRadius + tailSize, size.height);
      path.quadraticBezierTo(tailSize, size.height, tailSize, size.height - cornerRadius);
      path.lineTo(tailSize, size.height - cornerRadius - tailSize);
      // 左下角尖角
      path.lineTo(0, size.height);
      path.lineTo(tailSize, size.height - tailSize);
      path.lineTo(tailSize, cornerRadius);
      path.quadraticBezierTo(tailSize, 0, cornerRadius + tailSize, 0);
    }
    
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
