// lib/src/core/theme/app_text_styles.dart

import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppTextStyles {
  // --- 主要标题 (页面标题、大标题) ---
  static const TextStyle headline1 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.primaryText,
  );

  // --- 次级标题 (卡片标题、分组标题) ---
  static const TextStyle headline2 = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: AppColors.primaryText,
  );

  // --- 正文 (主要描述、聊天内容) ---
  static const TextStyle body = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.primaryText,
    height: 1.5, // 增加行高，提升可读性
  );

  // --- 次要正文/按钮文字 ---
  static const TextStyle bodySecondary = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500, // Medium weight
    color: AppColors.secondaryText,
  );

  // --- 说明文字/标签 (最小号字) ---
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.tertiaryText,
  );

  // --- 特殊样式 ---
  static const TextStyle button = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: Colors.black, // 默认亮色按钮配深色字
  );

  // 旧的样式，保持兼容
  static const TextStyle appBarTitle = headline2;
  static const TextStyle statusText = bodySecondary;
  static const TextStyle introText = body;
  static const TextStyle messageText = body;
} 
