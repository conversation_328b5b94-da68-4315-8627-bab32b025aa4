import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';

class FriendsPage extends StatefulWidget {
  const FriendsPage({super.key});

  @override
  State<FriendsPage> createState() => _FriendsPageState();
}

class _FriendsPageState extends State<FriendsPage> {
  // 控制各个关系分组的折叠状态
  final Map<String, bool> _collapsedSections = {
    '初见': false,
    '恋人': true,
    '朋友': true,
    '家人': true,
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: const Text(
          '羁绊',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryText,
          ),
        ),
        actions: [
          TextButton.icon(
            onPressed: () {
              // TODO: 跳转到心伴小窗
            },
            icon: const Icon(Icons.add_circle_outline, color: AppColors.primaryText),
            label: const Text(
              '心伴小窗',
              style: TextStyle(color: AppColors.primaryText, fontSize: 14),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildTopFunctionCards(),
            const SizedBox(height: 16),
            _buildRelationshipSection(
              title: '初见',
              levelRequired: 1,
              characters: _getInitialCharacters(),
            ),
            _buildRelationshipSection(
              title: '恋人',
              levelRequired: 10,
              characters: _getLoverCharacters(),
            ),
            _buildRelationshipSection(
              title: '朋友',
              levelRequired: 5,
              characters: _getFriendCharacters(),
            ),
            _buildRelationshipSection(
              title: '家人',
              levelRequired: 15,
              characters: _getFamilyCharacters(),
            ),
            const SizedBox(height: 100), // 底部留白
          ],
        ),
      ),
    );
  }

  // 构建顶部功能卡片
  Widget _buildTopFunctionCards() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildFunctionCard(
                  title: '拾光之约',
                  subtitle: '与TA的美好回忆',
                  icon: Icons.photo_library,
                  color: AppColors.accentYellow,
                  onTap: () {
                    // TODO: 跳转到拾光之约
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildFunctionCard(
                  title: '留影室',
                  subtitle: '记录珍贵瞬间',
                  icon: Icons.camera_alt,
                  color: AppColors.accentBlue,
                  onTap: () {
                    // TODO: 跳转到留影室
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildFunctionCard(
            title: '次元诊疗',
            subtitle: '了解TA的内心世界，提升羁绊关系',
            icon: Icons.psychology,
            color: AppColors.accentPurple,
            isWide: true,
            onTap: () {
              // TODO: 跳转到次元诊疗
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    bool isWide = false,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.secondaryBg,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
          // --- v 核心修改 v ---
          // 添加辉光效果
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.4), // 使用卡片的主题色作为辉光颜色
              blurRadius: 15.0,           // 模糊半径，决定辉光范围
              spreadRadius: 1.0,            // 扩散半径，让辉光更柔和
            ),
          ],
          // --- ^ 核心修改 ^ ---
        ),
        child: isWide
            ? Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            color: AppColors.primaryText,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: const TextStyle(
                            color: AppColors.secondaryText,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(icon, color: color, size: 20),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    title,
                    style: const TextStyle(
                      color: AppColors.primaryText,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  // 构建关系分组
  Widget _buildRelationshipSection({
    required String title,
    required int levelRequired,
    required List<Map<String, dynamic>> characters,
  }) {
    final isCollapsed = _collapsedSections[title] ?? false;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.secondaryBg,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _collapsedSections[title] = !isCollapsed;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: AppColors.primaryText,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.accentPurple.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Lv.$levelRequired',
                      style: const TextStyle(
                        color: AppColors.accentPurple,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    isCollapsed ? Icons.expand_more : Icons.expand_less,
                    color: AppColors.secondaryText,
                  ),
                ],
              ),
            ),
          ),
          if (!isCollapsed) ...[
            const Divider(color: AppColors.inputBackground, height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.8,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: characters.length,
                itemBuilder: (context, index) {
                  return _buildCharacterCard(characters[index]);
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 构建角色卡片
  Widget _buildCharacterCard(Map<String, dynamic> character) {
    // 模拟解锁度
    final double unlockProgress = (character['bondProgress'] as int).toDouble() / 100.0;
    
    return InkWell(
      onTap: () {
        // TODO: 跳转到角色详情或聊天
        context.push('/chat/${character['chatId']}');
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.inputBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: character['isUnlocked'] 
                ? AppColors.accentPurple.withOpacity(0.3)
                : AppColors.secondaryText.withOpacity(0.2),
          ),
        ),
        child: Column(
          children: [
            Expanded(
              child: Stack(
                fit: StackFit.expand,
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                    child: Image.network(
                      character['avatar'],
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: AppColors.secondaryBg,
                          child: const Icon(
                            Icons.person,
                            color: AppColors.secondaryText,
                            size: 48,
                          ),
                        );
                      },
                    ),
                  ),
                  if (!character['isUnlocked'])
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.lock,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                    ),
                  // --- ▼▼▼ 新增环形解锁度进度条 ▼▼▼ ---
                  Positioned(
                    bottom: 8,
                    right: 8,
                    child: SizedBox(
                      width: 40,
                      height: 40,
                      child: CustomPaint(
                        painter: _CircularProgressPainter(
                          progress: unlockProgress,
                          backgroundColor: Colors.white.withOpacity(0.2),
                          progressColor: AppColors.accentYellow,
                        ),
                        child: Center(
                          child: Text(
                            '${(unlockProgress * 100).toInt()}%',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                children: [
                  Text(
                    character['name'],
                    style: const TextStyle(
                      color: AppColors.primaryText,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  // --- ▼▼▼ 修改为解锁条件文本 ▼▼▼ ---
                  Text(
                    '羁绊值Lv.1解锁日记', // 替换为动态解锁条件
                    style: const TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 模拟数据
  List<Map<String, dynamic>> _getInitialCharacters() {
    return [
      {
        'name': '小雪',
        'avatar': 'https://i.imgur.com/avatar1.png',
        'level': 3,
        'bondProgress': 45,
        'isUnlocked': true,
        'chatId': 'chat_xiaoxue',
      },
      {
        'name': '小雨',
        'avatar': 'https://i.imgur.com/avatar2.png',
        'level': 1,
        'bondProgress': 20,
        'isUnlocked': true,
        'chatId': 'chat_xiaoyu',
      },
    ];
  }

  List<Map<String, dynamic>> _getLoverCharacters() {
    return [
      {
        'name': '心上人',
        'avatar': 'https://i.imgur.com/lover1.png',
        'level': 12,
        'bondProgress': 85,
        'isUnlocked': false,
        'chatId': 'chat_lover1',
      },
    ];
  }

  List<Map<String, dynamic>> _getFriendCharacters() {
    return [
      {
        'name': '好朋友',
        'avatar': 'https://i.imgur.com/friend1.png',
        'level': 7,
        'bondProgress': 60,
        'isUnlocked': false,
        'chatId': 'chat_friend1',
      },
    ];
  }

  List<Map<String, dynamic>> _getFamilyCharacters() {
    return [
      {
        'name': '家人',
        'avatar': 'https://i.imgur.com/family1.png',
        'level': 18,
        'bondProgress': 95,
        'isUnlocked': false,
        'chatId': 'chat_family1',
      },
    ];
  }
}

// 在 _FriendsPageState 类的外部，文件的底部添加这个 Painter
class _CircularProgressPainter extends CustomPainter {
  final double progress; // 0.0 to 1.0
  final Color backgroundColor;
  final Color progressColor;
  final double strokeWidth;

  _CircularProgressPainter({
    required this.progress,
    required this.backgroundColor,
    required this.progressColor,
    this.strokeWidth = 4.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw background circle
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;
    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw progress arc
    final progressPaint = Paint()
      ..color = progressColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    double sweepAngle = 2 * 3.1415926535 * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -3.1415926535 / 2, // Start from the top
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
