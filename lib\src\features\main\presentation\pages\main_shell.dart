import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';

class MainShell extends StatefulWidget {
  final StatefulNavigationShell navigationShell;

  const MainShell({super.key, required this.navigationShell});

  @override
  State<MainShell> createState() => _MainShellState();
}

class _MainShellState extends State<MainShell> {
  void _onItemTapped(int index) {
    // 索引为2是创作按钮的占位符，实际点击由自定义按钮处理
    if (index == 2) {
      // 使用 GoRouter 的 push 方法，以覆盖方式弹出创作中心
      context.push('/creation_center_page'); // 确保路由存在
      return;
    }
    widget.navigationShell.goBranch(
      index,
      initialLocation: index == widget.navigationShell.currentIndex,
    );
  }

  // 修复：selectedIndex的计算逻辑
  int _calculateSelectedIndex(BuildContext context) {
    return widget.navigationShell.currentIndex;
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 1. 将 body 直接包裹在一个 Container 中
      body: Container(
        // 2. 为 Container 添加 BoxDecoration 以实现渐变
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primaryGradientStart, // 使用新定义的渐变起始色
              AppColors.primaryGradientEnd,   // 使用新定义的渐变结束色
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: widget.navigationShell, // 3. 将 navigationShell作为 child
      ),
      // --- v 核心修改: 移除FAB，完全重构BottomNavigationBar v ---
      bottomNavigationBar: BottomAppBar(
        color: AppColors.background,
        elevation: 0,
        child: SizedBox(
          height: 60, // 底部导航栏高度
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: <Widget>[
              _buildNavItem(icon: Icons.message_outlined, label: '聊天', index: 0),
              _buildNavItem(icon: Icons.people_outline, label: '羁绊', index: 1), // "好友" 改为 "羁绊"
              _buildCreationButton(), // 中间的创作按钮
              _buildNavItem(icon: Icons.explore_outlined, label: '推荐', index: 3),
              _buildNavItem(icon: Icons.person_outline, label: '我的', index: 4),
            ],
          ),
        ),
      ),
      // 移除 FloatingActionButton 和其位置属性
      // floatingActionButton: ...,
      // floatingActionButtonLocation: ...,
      // --- ^ 核心修改 ^ ---
    );
  }

  // --- v 新增方法：构建普通导航项 v ---
  Widget _buildNavItem({required IconData icon, required String label, required int index}) {
    // 修复：分支索引大于等于2时，UI索引需要+1
    final selectedUiIndex = _calculateSelectedIndex(context);
    final isSelected = selectedUiIndex == index;

    // 分支索引2是创作按钮的占位符，UI上不存在，直接跳过
    // 导航到第3和第4个分支时，其UI索引是3和4
    final branchIndex = index > 2 ? index - 1 : index;

    return Expanded(
      child: InkWell(
        onTap: () => _onItemTapped(branchIndex),
        borderRadius: BorderRadius.circular(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // --- v 核心修改 v ---
            AnimatedScale(
              scale: isSelected ? 1.2 : 1.0, // 选中时放大1.2倍
              duration: const Duration(milliseconds: 200), // 动画时长
              curve: Curves.easeOut, // 动画曲线
              child: Icon(
                icon,
                color: isSelected ? AppColors.accentPurple : AppColors.secondaryText,
              ),
            ),
            // --- ^ 核心修改 ^ ---
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? AppColors.accentPurple : AppColors.secondaryText,
              ),
            ),
          ],
        ),
      ),
    );
  }
  // --- ^ 新增方法 ^ ---

  // --- v 新增方法：构建自定义创作按钮 v ---
  Widget _buildCreationButton() {
    return InkWell(
      onTap: () => _onItemTapped(2),
      child: Container(
        width: 48, // 按钮宽度
        height: 32, // 按钮高度
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8), // 圆角矩形
          gradient: LinearGradient(
            colors: [
              Colors.white.withOpacity(0.25),
              Colors.white.withOpacity(0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          border: Border.all(color: Colors.white.withOpacity(0.4), width: 1.5),
        ),
        child: const Icon(Icons.add, color: Colors.white, size: 20),
      ),
    );
  }
  // --- ^ 新增方法 ^ ---
} 
